// import React, { useState, useEffect } from 'react';
// import { Outlet, useLocation } from 'react-router-dom';
// import AppTopBar from '@/components/layout/AppTopBar';
// import PrimarySidebar from '@/components/layout/PrimarySidebar';
// import SecondarySidebar from '@/components/layout/SecondarySidebar';
// import { useAuth } from '@/contexts/AuthContext';
// import { DNoteLogo } from '@/components/DNoteLogo';
// import { cn } from '@/lib/utils';
// import { mainNavItems, toolsNavItems, systemNavItems, aiFeaturesNavItems, settingsNavItems } from '@/components/layout/sidebarConfig';

// const AppLayout = ({ children }) => {
//   const { currentUser, userProfile } = useAuth();
//   const location = useLocation();

//   const [isPrimarySidebarPinned, setIsPrimarySidebarPinned] = useState(true);
//   const [isPrimarySidebarHovered, setIsPrimarySidebarHovered] = useState(false);
//   const [activePrimaryItem, setActivePrimaryItem] = useState(null);
//   const [secondarySidebarItems, setSecondarySidebarItems] = useState([]);
//   const [isMobilePrimarySidebarOpen, setIsMobilePrimarySidebarOpen] = useState(false);

//   const isPrimaryExpanded = isPrimarySidebarPinned || isPrimarySidebarHovered;

//   useEffect(() => {
//     // Determine active primary item and secondary items based on current path
//     const currentPath = location.pathname;
//     let foundActiveItem = null;

//     [...mainNavItems, ...toolsNavItems, ...systemNavItems].forEach(item => {
//       if (currentPath.startsWith(item.basePath || item.to)) {
//         foundActiveItem = item;
//       }
//     });

//     setActivePrimaryItem(foundActiveItem);

//     if (foundActiveItem) {
//       if (foundActiveItem.id === 'ai-features') setSecondarySidebarItems(aiFeaturesNavItems);
//       else if (foundActiveItem.id === 'settings') setSecondarySidebarItems(settingsNavItems);
//       // Add more conditions for other primary items with sub-menus
//       else setSecondarySidebarItems([]);
//     } else {
//       setSecondarySidebarItems([]);
//     }

//     // Close mobile sidebar on path change
//     if (isMobilePrimarySidebarOpen) {
//       setIsMobilePrimarySidebarOpen(false);
//     }

//   }, [location.pathname, isMobilePrimarySidebarOpen]);

//   const handlePrimarySidebarPin = () => setIsPrimarySidebarPinned(!isPrimarySidebarPinned);
//   const handlePrimarySidebarMouseEnter = () => setIsPrimarySidebarHovered(true);
//   const handlePrimarySidebarMouseLeave = () => setIsPrimarySidebarHovered(false);

//   const handlePrimaryItemClick = (item) => {
//     setActivePrimaryItem(item);
//     // Logic for secondary sidebar items based on clicked primary item
//     if (item.id === 'dashboard') {
//       setSecondarySidebarItems([]);
//     } else if (item.subItems) {
//       setSecondarySidebarItems(item.subItems);
//     } else if (item.id === 'ai-features') { // Example
//       setSecondarySidebarItems(aiFeaturesNavItems);
//     } else if (item.id === 'settings') {
//       setSecondarySidebarItems(settingsNavItems);
//     }
//      else {
//       setSecondarySidebarItems([]);
//     }
//     if (isMobilePrimarySidebarOpen) setIsMobilePrimarySidebarOpen(false);
//   };

//   const toggleMobilePrimarySidebar = () => setIsMobilePrimarySidebarOpen(!isMobilePrimarySidebarOpen);

//   if (!currentUser) {
//     return (
//       <div className="flex flex-col items-center justify-center min-h-screen bg-slate-100 dark:bg-slate-900 p-4">
//         <DNoteLogo className="mb-8" textSize="text-4xl" />
//         <p className="text-xl text-slate-700 dark:text-slate-300">Authenticating...</p>
//       </div>
//     );
//   }

//   const getPlanName = () => {
//     if (userProfile?.profile_category === 'Organization' && userProfile?.organization_name) {
//       return userProfile.organization_name;
//     }
//     return userProfile?.plan_type || 'Free';
//   };

//   const primarySidebarWidth = isPrimaryExpanded ? 'w-60' : 'w-20'; // lg:w-60 : lg:w-20
//   const secondarySidebarWidth = secondarySidebarItems.length > 0 ? 'w-60' : 'w-0'; // lg:w-60 : w-0
//   const showSecondarySidebar = secondarySidebarItems.length > 0 && activePrimaryItem?.id !== 'dashboard';

//   return (
//     <div className="flex h-screen bg-slate-100 dark:bg-slate-900 text-slate-900 dark:text-slate-50">
//       <AppTopBar
//         projectName="Dnote"
//         planName={getPlanName()}
//         userProfile={userProfile}
//         onToggleMobilePrimarySidebar={toggleMobilePrimarySidebar}
//       />

//       <div className="flex flex-1 pt-16"> {/* pt-16 for top bar height */}
//         <PrimarySidebar
//           isExpanded={isPrimaryExpanded}
//           isPinned={isPrimarySidebarPinned}
//           onPinToggle={handlePrimarySidebarPin}
//           onMouseEnter={handlePrimarySidebarMouseEnter}
//           onMouseLeave={handlePrimarySidebarMouseLeave}
//           onPrimaryItemClick={handlePrimaryItemClick}
//           activePrimaryItemId={activePrimaryItem?.id}
//           isMobileOpen={isMobilePrimarySidebarOpen}
//           onMobileClose={toggleMobilePrimarySidebar}
//         />

//         {showSecondarySidebar && (
//           <SecondarySidebar
//             items={secondarySidebarItems}
//             title={activePrimaryItem?.label || "Sub Menu"}
//             parentPath={activePrimaryItem?.basePath || activePrimaryItem?.to}
//           />
//         )}

//         <main
//           className={cn(
//             "flex-1 overflow-y-auto transition-all duration-300 ease-in-out",
//             "custom-scrollbar"
//             // Dynamic margin based on sidebar states
//             // In production, use CSS variables or more complex logic if needed
//           )}
//           style={{
//             marginLeft: `calc(${isPrimaryExpanded ? '15rem' : '5rem'} + ${showSecondarySidebar ? '15rem' : '0rem'})`
//           }}
//         >
//           <div className="p-4 sm:p-6 lg:p-8">
//             {children || <Outlet />}
//           </div>
//         </main>
//       </div>
//     </div>
//   );
// };

// export default AppLayout;
// ---------------------------------------------------------------------------------------------------------------------------------------
import React, { useState, useEffect } from "react";
import { Outlet, useLocation } from "react-router-dom";
import AppTopBar from "@/components/layout/AppTopBar";
import PrimarySidebar from "@/components/layout/PrimarySidebar";
import { motion, AnimatePresence } from "framer-motion";
import SecondarySidebar from "@/components/layout/SecondarySidebar";
import { useAuth } from "@/contexts/AuthContext";
import { ThemeToggle } from "@/components/layout/ThemeToggle";
import { DNoteLogo } from "@/components/DNoteLogo";
import { cn } from "@/lib/utils";
import {
  mainNavItems,
  toolsNavItems,
  systemNavItems,
  aiFeaturesNavItems,
  settingsNavItems,
} from "@/components/layout/sidebarConfig";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";

const AppLayout = ({ children }) => {
  const { currentUser, userProfile } = useAuth();
  const location = useLocation();

  const [isPrimarySidebarPinned, setIsPrimarySidebarPinned] = useState(false); // Default to collapsed
  const [isPrimarySidebarHovered, setIsPrimarySidebarHovered] = useState(false);
  const [activePrimaryItem, setActivePrimaryItem] = useState(null);
  const [secondarySidebarItems, setSecondarySidebarItems] = useState([]);
  const [isMobilePrimarySidebarOpen, setIsMobilePrimarySidebarOpen] =
    useState(false);

  const isPrimaryExpanded = isPrimarySidebarPinned || isPrimarySidebarHovered;

  useEffect(() => {
    // Determine active primary item and secondary items based on current path
    const currentPath = location.pathname;
    let foundActiveItem = null;

    [...mainNavItems, ...toolsNavItems, ...systemNavItems].forEach((item) => {
      if (currentPath.startsWith(item.basePath || item.to)) {
        foundActiveItem = item;
      }
    });

    setActivePrimaryItem(foundActiveItem);

    if (foundActiveItem) {
      if (foundActiveItem.id === "ai-features")
        setSecondarySidebarItems(aiFeaturesNavItems);
      else if (foundActiveItem.id === "settings")
        setSecondarySidebarItems(settingsNavItems);
      // Add more conditions for other primary items with sub-menus
      else setSecondarySidebarItems([]);
    } else {
      setSecondarySidebarItems([]);
    }

    // Close mobile sidebar on path change
    if (isMobilePrimarySidebarOpen) {
      setIsMobilePrimarySidebarOpen(false);
    }
  }, [location.pathname, isMobilePrimarySidebarOpen]);

  const handlePrimarySidebarPin = () =>
    setIsPrimarySidebarPinned(!isPrimarySidebarPinned);
  const handlePrimarySidebarMouseEnter = () => setIsPrimarySidebarHovered(true);
  const handlePrimarySidebarMouseLeave = () =>
    setIsPrimarySidebarHovered(false);

  const handlePrimaryItemClick = (item) => {
    setActivePrimaryItem(item);
    // Logic for secondary sidebar items based on clicked primary item
    if (item.id === "dashboard") {
      setSecondarySidebarItems([]);
    } else if (item.subItems) {
      setSecondarySidebarItems(item.subItems);
    } else if (item.id === "ai-features") {
      setSecondarySidebarItems(aiFeaturesNavItems);
    } else if (item.id === "settings") {
      setSecondarySidebarItems(settingsNavItems);
    } else {
      setSecondarySidebarItems([]);
    }
    if (isMobilePrimarySidebarOpen) setIsMobilePrimarySidebarOpen(false);
  };

  const toggleMobilePrimarySidebar = () =>
    setIsMobilePrimarySidebarOpen(!isMobilePrimarySidebarOpen);

  if (!currentUser) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-slate-100 dark:bg-slate-900 p-4">
        <DNoteLogo className="mb-8" textSize="text-4xl" />
        <p className="text-xl text-slate-700 dark:text-slate-300">
          Authenticating...
        </p>
      </div>
    );
  }

  const getPlanName = () => {
    if (
      userProfile?.profile_category === "Organization" &&
      userProfile?.organization_name
    ) {
      return userProfile.organization_name;
    }
    return userProfile?.plan_type || "Free";
  };

  const primarySidebarWidth = isPrimaryExpanded ? "w-60" : "w-16";
  const secondarySidebarWidth =
    secondarySidebarItems.length > 0 ? "w-72" : "w-0";
  const showSecondarySidebar =
    secondarySidebarItems.length > 0 && activePrimaryItem?.id !== "dashboard";

  return (
    <div className="flex h-screen bg-white dark:bg-slate-900 text-slate-900 dark:text-slate-50">
      <AppTopBar
        projectName="Tools"
        planName={getPlanName()}
        userProfile={userProfile}
        onToggleMobilePrimarySidebar={toggleMobilePrimarySidebar}
      />

      <div className="flex flex-1 pt-16">
        <PrimarySidebar
          isExpanded={isPrimaryExpanded}
          isPinned={isPrimarySidebarPinned}
          onPinToggle={handlePrimarySidebarPin}
          onMouseEnter={handlePrimarySidebarMouseEnter}
          onMouseLeave={handlePrimarySidebarMouseLeave}
          onPrimaryItemClick={handlePrimaryItemClick}
          activePrimaryItemId={activePrimaryItem?.id}
          isMobileOpen={isMobilePrimarySidebarOpen}
          onMobileClose={toggleMobilePrimarySidebar}
        />

        {/* {showSecondarySidebar && (
          <SecondarySidebar
            items={secondarySidebarItems}
            title={activePrimaryItem?.label || "Sub Menu"}
            parentPath={activePrimaryItem?.basePath || activePrimaryItem?.to}
          />
        )} */}

        <main
          className={cn(
            "flex-1 overflow-y-auto transition-all duration-300 ease-in-out bg-slate-50 dark:bg-slate-800",
            "custom-scrollbar"
          )}
          style={{
            marginLeft: `calc(${isPrimaryExpanded ? "15rem" : "4rem"} + ${
              showSecondarySidebar ? "18rem" : "0rem"
            })`,
          }}
        >
          <div className="h-full">{children || <Outlet />}</div>
        </main>
        {/* <main className="flex-1 overflow-y-auto transition-all duration-300 ease-in-out bg-slate-50 dark:bg-slate-800 custom-scrollbar p-6 lg:p-8">
           <AnimatePresence mode="wait">
             <motion.div
              key={location.pathname}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
            >
              {children || <Outlet />}
            </motion.div>
          </AnimatePresence>
        </main> */}
      </div>
    </div>
  );
};

export default AppLayout;
// --------------------------------------------------------------------------------
// import React, { useState, useEffect } from 'react';
// import { Outlet, useLocation } from 'react-router-dom';
// import { motion, AnimatePresence } from 'framer-motion';
// import AppTopBar from '@/components/layout/AppTopBar';
// import PrimarySidebar from '@/components/layout/PrimarySidebar';
// import SecondarySidebar from '@/components/layout/SecondarySidebar';
// import { useAuth } from '@/contexts/AuthContext';
// import { DNoteLogo } from '@/components/DNoteLogo';
// import { cn } from '@/lib/utils';
// import { mainNavItems, toolsNavItems, systemNavItems, aiFeaturesNavItems, settingsNavItems } from '@/components/layout/sidebarConfig';

// const AppLayout = ({ children }) => {
//   const { currentUser, userProfile } = useAuth();
//   const location = useLocation();

//   const [isPrimarySidebarCollapsed, setIsPrimarySidebarCollapsed] = useState(false);
//   const [activePrimaryItem, setActivePrimaryItem] = useState(null);
//   const [secondarySidebarItems, setSecondarySidebarItems] = useState([]);
//   const [isMobilePrimarySidebarOpen, setIsMobilePrimarySidebarOpen] = useState(false);

//   useEffect(() => {
//     // Determine active primary item and secondary items based on current path
//     const currentPath = location.pathname;
//     let foundActiveItem = null;

//     [...mainNavItems, ...toolsNavItems, ...systemNavItems].forEach(item => {
//       if (currentPath.startsWith(item.basePath || item.to)) {
//         foundActiveItem = item;
//       }
//     });

//     setActivePrimaryItem(foundActiveItem);

//     if (foundActiveItem) {
//       if (foundActiveItem.id === 'ai-features') setSecondarySidebarItems(aiFeaturesNavItems);
//       else if (foundActiveItem.id === 'settings') setSecondarySidebarItems(settingsNavItems);
//       else setSecondarySidebarItems([]);
//     } else {
//       setSecondarySidebarItems([]);
//     }

//     // Close mobile sidebar on path change
//     if (isMobilePrimarySidebarOpen) {
//       setIsMobilePrimarySidebarOpen(false);
//     }
//   }, [location.pathname, isMobilePrimarySidebarOpen]);

//   const handlePrimaryItemClick = (item) => {
//     setActivePrimaryItem(item);
//     if (item.id === 'dashboard') {
//       setSecondarySidebarItems([]);
//     } else if (item.subItems) {
//       setSecondarySidebarItems(item.subItems);
//     } else if (item.id === 'ai-features') {
//       setSecondarySidebarItems(aiFeaturesNavItems);
//     } else if (item.id === 'settings') {
//       setSecondarySidebarItems(settingsNavItems);
//     } else {
//       setSecondarySidebarItems([]);
//     }
//     if (isMobilePrimarySidebarOpen) setIsMobilePrimarySidebarOpen(false);
//   };

//   const togglePrimarySidebar = () => setIsPrimarySidebarCollapsed(!isPrimarySidebarCollapsed);
//   const toggleMobilePrimarySidebar = () => setIsMobilePrimarySidebarOpen(!isMobilePrimarySidebarOpen);

//   if (!currentUser) {
//     return (
//       <div className="flex flex-col items-center justify-center min-h-screen bg-muted/30 dark:bg-background p-4">
//         <DNoteLogo className="mb-8" textSize="text-4xl" />
//         <p className="text-xl text-muted-foreground">Authenticating...</p>
//       </div>
//     );
//   }

//   const getPlanName = () => {
//     if (userProfile?.profile_category === 'Organization' && userProfile?.organization_name) {
//       return userProfile.organization_name;
//     }
//     return userProfile?.plan_type || 'Free';
//   };

//   const showSecondarySidebar = secondarySidebarItems.length > 0 && activePrimaryItem?.id !== 'dashboard';

//   const sidebarVariants = {
//     expanded: { width: '260px' },
//     collapsed: { width: '80px' },
//   };

//   const mobileSidebarVariants = {
//     open: { x: 0 },
//     closed: { x: "-100%" },
//   };

//   return (
//     <div className="flex h-screen bg-white dark:bg-slate-900 text-slate-900 dark:text-slate-50">
//       {/*  */}
//       {/* flex h-screen bg-muted/30 dark:bg-background text-foreground */}
//       {/* Desktop Primary Sidebar */}
//       <motion.aside
//         initial={false}
//         animate={isPrimarySidebarCollapsed ? 'collapsed' : 'expanded'}
//         variants={sidebarVariants}
//         transition={{ duration: 0.3, ease: 'easeInOut' }}
//         className="hidden md:flex flex-col bg-card border-r border-border fixed h-full z-30"
//       >
//         <PrimarySidebar
//           isCollapsed={isPrimarySidebarCollapsed}
//           onToggleCollapsed={togglePrimarySidebar}
//           onPrimaryItemClick={handlePrimaryItemClick}
//           activePrimaryItemId={activePrimaryItem?.id}
//           planName={getPlanName()}
//         />
//       </motion.aside>

//       {/* Mobile Primary Sidebar */}
//       <AnimatePresence>
//         {isMobilePrimarySidebarOpen && (
//           <motion.aside
//             initial="closed"
//             animate="open"
//             exit="closed"
//             variants={mobileSidebarVariants}
//             transition={{ type: 'spring', stiffness: 300, damping: 30 }}
//             className="md:hidden fixed inset-y-0 left-0 w-64 bg-card border-r border-border z-40"
//           >
//             <PrimarySidebar
//               isCollapsed={false}
//               onToggleCollapsed={togglePrimarySidebar}
//               onPrimaryItemClick={handlePrimaryItemClick}
//               activePrimaryItemId={activePrimaryItem?.id}
//               planName={getPlanName()}
//               isMobile={true}
//               onMobileClose={toggleMobilePrimarySidebar}
//             />
//           </motion.aside>
//         )}
//       </AnimatePresence>
//       {isMobilePrimarySidebarOpen && (
//         <div className="md:hidden fixed inset-0 bg-black/30 z-30" onClick={toggleMobilePrimarySidebar}></div>
//       )}

//       {/* Secondary Sidebar */}
//       {/* {showSecondarySidebar && (
//         <motion.aside
//           initial={{ width: 0, opacity: 0 }}
//           animate={{ width: '240px', opacity: 1 }}
//           exit={{ width: 0, opacity: 0 }}
//           transition={{ duration: 0.3, ease: 'easeInOut' }}
//           className={cn(
//             "hidden md:flex flex-col bg-card border-r border-border fixed h-full z-20",
//             isPrimarySidebarCollapsed ? "left-[80px]" : "left-[260px]"
//           )}
//         >
//           <SecondarySidebar
//             items={secondarySidebarItems}
//             title={activePrimaryItem?.label || "Sub Menu"}
//             parentPath={activePrimaryItem?.basePath || activePrimaryItem?.to}
//           />
//         </motion.aside>
//       )} */}

//       <div className={cn(
//         "flex-1 flex flex-col transition-all duration-300 ease-in-out",
//         isPrimarySidebarCollapsed ? 'md:ml-[50px]' : 'md:ml-[360px]'
//         // ,showSecondarySidebar && 'md:ml-[100px]'
//       )}>
//         {/* Top Bar */}
//         <AppTopBar
//           projectName="Tools"
//           planName={getPlanName()}
//           userProfile={userProfile}
//           onToggleMobilePrimarySidebar={toggleMobilePrimarySidebar}
//         />

//         {/* Main Content Area */}
//         <main className="flex-1 overflow-y-auto transition-all duration-300 ease-in-out bg-slate-50 dark:bg-slate-800 custom-scrollbar">
//           <AnimatePresence mode="wait">
//             <motion.div
//               key={location.pathname}
//               initial={{ opacity: 0, y: 20 }}
//               animate={{ opacity: 1, y: 0 }}
//               exit={{ opacity: 0, y: -20 }}
//               transition={{ duration: 0.2 }}
//             >
//               {children || <Outlet />}
//             </motion.div>
//           </AnimatePresence>
//         </main>

//       </div>
//     </div>
//   );
// };

// export default AppLayout;
