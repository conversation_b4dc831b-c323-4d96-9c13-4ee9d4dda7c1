import React, { useState, useEffect } from "react";
import { useParams, useNavigate, useSearchParams } from "react-router-dom";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import {
  ArrowLeft,
  Plus,
  Search,
  Share,
  MoreVertical,
  FileText,
  Calendar,
  Star,
  Lock,
  Pin,
  Trash2,
  Edit3,
  SortAsc,
  SortDesc,
  Menu,
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/lib/supabaseClient";
import { cn } from "@/lib/utils";

export default function NotebookNotesPage() {
  const { notebookId } = useParams();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { toast } = useToast();
  const { currentUser } = useAuth();

  // State
  const [notebook, setNotebook] = useState(null);
  const [notes, setNotes] = useState([]);
  const [selectedNote, setSelectedNote] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("updated_at");
  const [sortOrder, setSortOrder] = useState("desc");
  const [isLoading, setIsLoading] = useState(true);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editTitle, setEditTitle] = useState("");
  const [editContent, setEditContent] = useState("");

  // Fetch notebook and notes
  useEffect(() => {
    const fetchData = async () => {
      if (!currentUser || !notebookId) {
        setIsLoading(false);
        return;
      }

      try {
        // Fetch notebook
        const { data: notebookData, error: notebookError } = await supabase
          .from("notebooks")
          .select("*")
          .eq("id", notebookId)
          .single();

        if (notebookError) throw notebookError;
        setNotebook(notebookData);

        // Fetch notes - handle missing columns gracefully
        let notesQuery = supabase
          .from("notes")
          .select("*")
          .eq("notebook_id", notebookId)
          .eq("user_id", currentUser.id);

        // Only filter by is_trashed if the column exists
        // For now, we'll skip this filter to avoid errors

        const ascending = sortOrder === "asc";
        notesQuery = notesQuery.order(sortBy, { ascending });

        const { data: notesData, error: notesError } = await notesQuery;
        if (notesError) throw notesError;

        setNotes(notesData || []);

        // Auto-select first note or note from URL
        const noteIdFromUrl = searchParams.get("note");
        if (noteIdFromUrl) {
          const noteFromUrl = notesData?.find((n) => n.id === noteIdFromUrl);
          if (noteFromUrl) {
            setSelectedNote(noteFromUrl);
          }
        } else if (notesData && notesData.length > 0) {
          setSelectedNote(notesData[0]);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        toast({
          title: "Error",
          description: "Failed to load notebook data",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [notebookId, currentUser, sortBy, sortOrder, searchParams, toast]);

  // Filter notes based on search
  const filteredNotes = notes.filter((note) => {
    const term = searchTerm.toLowerCase();
    return (
      !term ||
      (note.title && note.title.toLowerCase().includes(term)) ||
      (note.content_html && note.content_html.toLowerCase().includes(term)) ||
      (note.content && note.content.toLowerCase().includes(term)) ||
      (note.tags &&
        Array.isArray(note.tags) &&
        note.tags.some((tag) => tag.toLowerCase().includes(term)))
    );
  });

  const handleCreateNote = async () => {
    if (!currentUser || !notebookId) {
      toast({
        title: "Error",
        description: "Missing user or notebook information",
        variant: "destructive",
      });
      return;
    }

    try {
      console.log(
        "Creating note for user:",
        currentUser.id,
        "in notebook:",
        notebookId
      );

      // Basic note data that should always work
      const noteData = {
        title: "Untitled",
        notebook_id: notebookId,
        user_id: currentUser.id,
      };

      // Add optional fields if they exist in the table
      try {
        noteData.content_html = "<p>Start writing...</p>";
        noteData.is_trashed = false;
        noteData.is_favorite = false;
        noteData.is_locked = false;
        noteData.is_sticky = false;
        noteData.tags = [];
      } catch (e) {
        console.log("Some optional fields not available:", e);
      }

      console.log("Note data:", noteData);

      const { data, error } = await supabase
        .from("notes")
        .insert(noteData)
        .select()
        .single();

      if (error) {
        console.error("Supabase error:", error);
        throw error;
      }

      console.log("Created note:", data);
      setNotes([data, ...notes]);
      setSelectedNote(data);

      toast({
        title: "Note Created",
        description: "New note has been created",
      });
    } catch (error) {
      console.error("Error creating note:", error);
      toast({
        title: "Error",
        description: `Failed to create note: ${error.message}`,
        variant: "destructive",
      });
    }
  };

  const handleEditNote = () => {
    if (!selectedNote) return;
    setIsEditing(true);
    setEditTitle(selectedNote.title || "");
    setEditContent(selectedNote.content_html || selectedNote.content || "");
  };

  const handleSaveNote = async () => {
    if (!selectedNote) return;

    try {
      const updateData = {
        title: editTitle || "Untitled",
        updated_at: new Date().toISOString(),
      };

      // Add content field based on what exists in the table
      if (selectedNote.content_html !== undefined) {
        updateData.content_html = editContent;
      } else {
        updateData.content = editContent;
      }

      const { data, error } = await supabase
        .from("notes")
        .update(updateData)
        .eq("id", selectedNote.id)
        .eq("user_id", currentUser.id)
        .select()
        .single();

      if (error) throw error;

      // Update local state
      setNotes(
        notes.map((note) => (note.id === selectedNote.id ? data : note))
      );
      setSelectedNote(data);
      setIsEditing(false);

      toast({
        title: "Note Saved",
        description: "Your changes have been saved",
      });
    } catch (error) {
      console.error("Error saving note:", error);
      toast({
        title: "Error",
        description: `Failed to save note: ${error.message}`,
        variant: "destructive",
      });
    }
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditTitle("");
    setEditContent("");
  };

  const handleDeleteNote = async (noteId) => {
    if (!confirm("Are you sure you want to delete this note?")) return;

    try {
      const { error } = await supabase
        .from("notes")
        .delete()
        .eq("id", noteId)
        .eq("user_id", currentUser.id);

      if (error) throw error;

      setNotes(notes.filter((note) => note.id !== noteId));
      if (selectedNote?.id === noteId) {
        setSelectedNote(notes.find((note) => note.id !== noteId) || null);
      }

      toast({
        title: "Note Deleted",
        description: "Note has been deleted",
      });
    } catch (error) {
      console.error("Error deleting note:", error);
      toast({
        title: "Error",
        description: `Failed to delete note: ${error.message}`,
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return "Today";
    if (diffDays === 2) return "Yesterday";
    if (diffDays <= 7) return `${diffDays} days ago`;
    return date.toLocaleDateString();
  };

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-slate-50 dark:bg-slate-900">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!notebook) {
    return (
      <div className="h-screen flex items-center justify-center bg-slate-50 dark:bg-slate-900">
        <div className="text-center">
          <FileText className="mx-auto h-16 w-16 text-slate-400 mb-4" />
          <h2 className="text-xl font-semibold text-slate-600 dark:text-slate-400 mb-2">
            Notebook not found
          </h2>
          <Button onClick={() => navigate("/notebooks")} variant="outline">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Notebooks
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex bg-gradient-to-br from-slate-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 overflow-hidden">
      {/* Sidebar */}
      <div
        className={cn(
          "flex flex-col bg-white dark:bg-slate-800 text-slate-800 dark:text-white transition-all duration-300 border-r border-slate-200 dark:border-slate-700/50 shadow-lg backdrop-blur-sm",
          isSidebarCollapsed ? "w-16" : "w-80"
        )}
      >
        {/* Header */}
        <div className="p-6 border-b border-slate-200 dark:border-slate-700/50 bg-white dark:bg-slate-800 backdrop-blur-sm">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
                className="text-slate-600 dark:text-slate-300 hover:text-slate-800 dark:hover:text-white hover:bg-slate-100 dark:hover:bg-slate-700"
              >
                <Menu className="h-4 w-4" />
              </Button>
              {!isSidebarCollapsed && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigate("/notebooks")}
                  className="text-slate-600 dark:text-slate-300 hover:text-slate-800 dark:hover:text-white hover:bg-slate-100 dark:hover:bg-slate-700"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back
                </Button>
              )}
            </div>
            {!isSidebarCollapsed && (
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-slate-300 hover:text-white hover:bg-slate-700"
                >
                  <Share className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-slate-300 hover:text-white hover:bg-slate-700"
                >
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </div>
            )}
          </div>

          {!isSidebarCollapsed && (
            <>
              <h1 className="text-2xl font-bold mb-2 text-slate-800 dark:text-white bg-gradient-to-r from-slate-800 to-blue-600 dark:from-white dark:to-blue-200 bg-clip-text text-transparent">
                {notebook.title}
              </h1>
              <p className="text-sm text-slate-600 dark:text-slate-300 mb-6 flex items-center gap-2">
                <span className="inline-flex items-center justify-center w-6 h-6 bg-blue-500/20 rounded-full text-xs font-medium text-blue-600 dark:text-blue-300">
                  {filteredNotes.length}
                </span>
                {filteredNotes.length === 1 ? "note" : "notes"}
              </p>

              {/* Search */}
              <div className="relative mb-6">
                <Search className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-slate-400" />
                <Input
                  placeholder="Search notes..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-12 pr-4 py-3 bg-slate-50 dark:bg-slate-700/50 border-slate-200 dark:border-slate-600/50 text-slate-800 dark:text-white placeholder:text-slate-400 focus:border-blue-500 focus:bg-white dark:focus:bg-slate-700 rounded-xl backdrop-blur-sm transition-all duration-200"
                />
              </div>

              {/* Sort Controls */}
              <div className="flex items-center gap-2 mb-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() =>
                    setSortOrder(sortOrder === "asc" ? "desc" : "asc")
                  }
                  className="text-slate-600 dark:text-slate-300 hover:text-slate-800 dark:hover:text-white hover:bg-slate-100 dark:hover:bg-slate-700"
                >
                  {sortOrder === "asc" ? (
                    <SortAsc className="h-4 w-4" />
                  ) : (
                    <SortDesc className="h-4 w-4" />
                  )}
                </Button>
                <Button
                  size="sm"
                  onClick={handleCreateNote}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white flex-1 rounded-lg px-4 py-2 shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  New Note
                </Button>
              </div>
            </>
          )}
        </div>

        {/* Notes List */}
        <div className="flex-1 overflow-y-auto">
          {!isSidebarCollapsed &&
            filteredNotes.map((note) => (
              <motion.div
                key={note.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                onClick={() => setSelectedNote(note)}
                className={cn(
                  "p-5 border-b border-slate-200 dark:border-slate-700/30 cursor-pointer hover:bg-slate-50 dark:hover:bg-slate-700/50 transition-all duration-300 group",
                  selectedNote?.id === note.id &&
                    "bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-600/20 dark:to-purple-600/20 border-l-4 border-l-blue-500 shadow-lg"
                )}
              >
                <div className="flex items-start justify-between mb-3">
                  <h3 className="font-semibold text-slate-800 dark:text-white line-clamp-1 text-base group-hover:text-blue-600 dark:group-hover:text-blue-200 transition-colors">
                    {note.title || "Untitled"}
                  </h3>
                  <div className="flex items-center gap-1 ml-2">
                    {note.is_favorite && (
                      <Star className="h-3 w-3 text-yellow-400 fill-current" />
                    )}
                    {note.is_locked && (
                      <Lock className="h-3 w-3 text-red-400" />
                    )}
                    {note.is_sticky && (
                      <Pin className="h-3 w-3 text-blue-400" />
                    )}
                  </div>
                </div>
                <p className="text-xs text-slate-600 dark:text-slate-300 line-clamp-2 mb-2 leading-relaxed">
                  {note.content_html
                    ? note.content_html
                        .replace(/<[^>]*>/g, "")
                        .substring(0, 80) + "..."
                    : note.content
                    ? note.content.substring(0, 80) + "..."
                    : "No content"}
                </p>
                <div className="flex items-center justify-between text-xs text-slate-500 dark:text-slate-400">
                  <span>{formatDate(note.updated_at)}</span>
                  {note.tags && note.tags.length > 0 && (
                    <span className="bg-slate-200 dark:bg-slate-600 text-slate-700 dark:text-slate-300 px-2 py-1 rounded text-xs">
                      {note.tags[0]}
                    </span>
                  )}
                </div>
              </motion.div>
            ))}

          {!isSidebarCollapsed && filteredNotes.length === 0 && (
            <div className="p-8 text-center">
              <FileText className="mx-auto h-12 w-12 text-slate-400 dark:text-slate-500 mb-4" />
              <p className="text-slate-600 dark:text-slate-400 mb-4 text-sm">
                {searchTerm
                  ? "No notes match your search"
                  : "No notes in this notebook"}
              </p>
              {!searchTerm && (
                <Button
                  onClick={handleCreateNote}
                  variant="outline"
                  size="sm"
                  className="border-slate-300 dark:border-slate-600 text-slate-600 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Note
                </Button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col bg-white dark:bg-slate-800 relative overflow-hidden">
        {selectedNote ? (
          <>
            {/* Note Header */}
            <div className="bg-white/95 dark:bg-slate-800/95 backdrop-blur-sm border-b border-slate-200 dark:border-slate-700/50 p-6 shadow-sm">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div>
                    <h2 className="text-2xl font-bold text-slate-800 dark:text-slate-200 mb-1">
                      {isEditing
                        ? editTitle || "Untitled"
                        : selectedNote.title || "Untitled"}
                      {isEditing && (
                        <span className="text-sm text-blue-600 ml-2">
                          (Editing)
                        </span>
                      )}
                    </h2>
                    <p className="text-sm text-slate-500 dark:text-slate-400">
                      Last edited on {formatDate(selectedNote.updated_at)}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {isEditing ? (
                    <>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleCancelEdit}
                      >
                        Cancel
                      </Button>
                      <Button
                        size="sm"
                        onClick={handleSaveNote}
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                      >
                        Save Changes
                      </Button>
                    </>
                  ) : (
                    <>
                      <Button variant="outline" size="sm">
                        <Share className="h-4 w-4 mr-2" />
                        Share
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleEditNote}
                      >
                        <Edit3 className="h-4 w-4 mr-2" />
                        Edit
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteNote(selectedNote.id)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* Note Content */}
            <div className="flex-1 p-8 bg-gradient-to-br from-white via-slate-50 to-blue-50 dark:from-slate-800 dark:via-slate-800 dark:to-slate-900 overflow-y-auto">
              <div className="max-w-4xl mx-auto">
                {isEditing ? (
                  <div className="space-y-6 bg-white dark:bg-slate-800 rounded-2xl p-8 shadow-xl border border-slate-200 dark:border-slate-700">
                    {/* Title Editor */}
                    <div>
                      <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                        Title
                      </label>
                      <Input
                        value={editTitle}
                        onChange={(e) => setEditTitle(e.target.value)}
                        placeholder="Note title..."
                        className="text-2xl font-bold border-0 bg-transparent focus:ring-2 focus:ring-blue-500 rounded-lg p-4"
                      />
                    </div>

                    {/* Content Editor */}
                    <div>
                      <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                        Content
                      </label>
                      <textarea
                        value={editContent.replace(/<[^>]*>/g, "")} // Strip HTML for editing
                        onChange={(e) =>
                          setEditContent(
                            `<p>${e.target.value.replace(/\n/g, "</p><p>")}</p>`
                          )
                        }
                        placeholder="Start writing your note..."
                        className="w-full h-96 p-6 border border-slate-300 dark:border-slate-600 rounded-xl bg-slate-50 dark:bg-slate-700 text-slate-900 dark:text-slate-100 resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-lg leading-relaxed"
                      />
                    </div>
                  </div>
                ) : (
                  <div className="bg-white dark:bg-slate-800 rounded-2xl p-8 shadow-xl border border-slate-200 dark:border-slate-700">
                    <div
                      className="prose prose-xl dark:prose-invert max-w-none leading-relaxed prose-headings:text-slate-800 dark:prose-headings:text-slate-200 prose-p:text-slate-700 dark:prose-p:text-slate-300"
                      dangerouslySetInnerHTML={{
                        __html:
                          selectedNote.content_html ||
                          (selectedNote.content
                            ? `<p>${selectedNote.content}</p>`
                            : null) ||
                          '<p class="text-slate-500 italic text-center py-12">This note is empty. Click Edit to start writing...</p>',
                      }}
                    />
                  </div>
                )}
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center bg-gradient-to-br from-white via-slate-50 to-blue-50 dark:from-slate-800 dark:via-slate-800 dark:to-slate-900">
            <div className="text-center max-w-md">
              <div className="bg-white dark:bg-slate-800 rounded-3xl p-12 shadow-2xl border border-slate-200 dark:border-slate-700">
                <div className="bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-8">
                  <FileText className="h-12 w-12 text-blue-600 dark:text-blue-400" />
                </div>
                <h2 className="text-3xl font-bold text-slate-800 dark:text-slate-200 mb-4">
                  Select a note to view
                </h2>
                <p className="text-slate-600 dark:text-slate-400 mb-8 leading-relaxed text-lg">
                  Choose a note from the sidebar to read its content, or create
                  a new note to get started.
                </p>
                <Button
                  onClick={handleCreateNote}
                  size="lg"
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 text-lg shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <Plus className="h-5 w-5 mr-2" />
                  Create New Note
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
